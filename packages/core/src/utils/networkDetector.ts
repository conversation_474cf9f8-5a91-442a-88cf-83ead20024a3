/**
 * 网络状态检测工具
 * 用于检测设备的网络连接状态，支持在线/离线检测和状态监听
 */

// 网络连接接口定义
interface NetworkConnection extends EventTarget {
  type?: string
  effectiveType?: string
  addEventListener(type: string, listener: EventListener): void
  removeEventListener(type: string, listener: EventListener): void
}

// 扩展Navigator接口
interface NavigatorWithConnection extends Navigator {
  connection?: NetworkConnection
}

export interface NetworkStatus {
  /** 是否在线 */
  isOnline: boolean
  /** 网络类型 */
  type?: string
  /** 有效连接类型 */
  effectiveType?: string
}

export type NetworkChangeCallback = (status: NetworkStatus) => void

class NetworkDetector {
  private static instance: NetworkDetector
  private callbacks: Set<NetworkChangeCallback> = new Set()
  private currentStatus: NetworkStatus = { isOnline: true }

  private constructor() {
    if (typeof window !== 'undefined') {
      this.initializeNetworkDetection()
    }
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): NetworkDetector {
    if (!NetworkDetector.instance) {
      NetworkDetector.instance = new NetworkDetector()
    }
    return NetworkDetector.instance
  }

  /**
   * 初始化网络检测
   */
  private initializeNetworkDetection(): void {
    // 初始状态
    this.updateNetworkStatus()

    // 监听在线/离线事件
    window.addEventListener('online', this.handleOnline.bind(this))
    window.addEventListener('offline', this.handleOffline.bind(this))

    // 监听连接状态变化（如果支持）
    if ('connection' in navigator) {
      const connection = (navigator as NavigatorWithConnection).connection
      if (connection && 'addEventListener' in connection) {
        connection.addEventListener('change', this.handleConnectionChange.bind(this))
      }
    }
  }

  /**
   * 处理在线事件
   */
  private handleOnline(): void {
    this.updateNetworkStatus()
  }

  /**
   * 处理离线事件
   */
  private handleOffline(): void {
    this.currentStatus = { isOnline: false }
    this.notifyCallbacks()
  }

  /**
   * 处理连接状态变化
   */
  private handleConnectionChange(): void {
    this.updateNetworkStatus()
  }

  /**
   * 更新网络状态
   */
  private updateNetworkStatus(): void {
    const isOnline = navigator.onLine
    let type: string | undefined
    let effectiveType: string | undefined

    // 获取网络连接信息（如果支持）
    if ('connection' in navigator) {
      const connection = (navigator as NavigatorWithConnection).connection
      if (connection) {
        type = connection.type
        effectiveType = connection.effectiveType
      }
    }

    const newStatus: NetworkStatus = {
      isOnline,
      type,
      effectiveType,
    }

    // 只有状态变化时才通知
    if (
      this.currentStatus.isOnline !== newStatus.isOnline ||
      this.currentStatus.type !== newStatus.type ||
      this.currentStatus.effectiveType !== newStatus.effectiveType
    ) {
      this.currentStatus = newStatus
      this.notifyCallbacks()
    }
  }

  /**
   * 通知所有回调函数
   */
  private notifyCallbacks(): void {
    this.callbacks.forEach((callback) => {
      try {
        callback(this.currentStatus)
      } catch (error) {
        console.error('网络状态回调执行错误:', error)
      }
    })
  }

  /**
   * 获取当前网络状态
   */
  public getNetworkStatus(): NetworkStatus {
    return { ...this.currentStatus }
  }

  /**
   * 检查是否在线
   */
  public isOnline(): boolean {
    return this.currentStatus.isOnline
  }

  /**
   * 检查是否离线
   */
  public isOffline(): boolean {
    return !this.currentStatus.isOnline
  }

  /**
   * 添加网络状态变化监听器
   */
  public addChangeListener(callback: NetworkChangeCallback): void {
    this.callbacks.add(callback)
  }

  /**
   * 移除网络状态变化监听器
   */
  public removeChangeListener(callback: NetworkChangeCallback): void {
    this.callbacks.delete(callback)
  }

  /**
   * 清除所有监听器
   */
  public clearAllListeners(): void {
    this.callbacks.clear()
  }

  /**
   * 销毁检测器，清理资源
   */
  public destroy(): void {
    if (typeof window !== 'undefined') {
      window.removeEventListener('online', this.handleOnline.bind(this))
      window.removeEventListener('offline', this.handleOffline.bind(this))

      if ('connection' in navigator) {
        const connection = (navigator as NavigatorWithConnection).connection
        if (connection && 'removeEventListener' in connection) {
          connection.removeEventListener('change', this.handleConnectionChange.bind(this))
        }
      }
    }

    this.clearAllListeners()
  }
}

// 导出单例实例
export const networkDetector = NetworkDetector.getInstance()

// 便捷的函数导出
export const isOnline = () => networkDetector.isOnline()
export const isOffline = () => networkDetector.isOffline()
export const getNetworkStatus = () => networkDetector.getNetworkStatus()
export const addNetworkChangeListener = (callback: NetworkChangeCallback) =>
  networkDetector.addChangeListener(callback)
export const removeNetworkChangeListener = (callback: NetworkChangeCallback) =>
  networkDetector.removeChangeListener(callback)

export default networkDetector
