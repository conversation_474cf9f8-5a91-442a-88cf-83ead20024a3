'use client'

import { useCallback } from 'react'

import { storeConfigSelector } from '../store'
import { useAppSelector } from '../store/hooks'
import { getDevicePixelRatio } from '../utils/util'

/**
 * 自定义 Hook：处理图片 URL 缩放
 *
 * 该 Hook 提供图片 URL 优化功能，包括：
 * - WebP 格式转换
 * - 设备像素比适配
 * - 动态尺寸调整
 * - URL 参数保留
 *
 * @returns {Function} scaleImageUrl 函数，用于处理图片 URL 的缩放
 *
 * @example
 * ```tsx
 * const MyComponent = () => {
 *   const scaleImageUrl = useScaleImageUrl();
 *
 *   return (
 *     <img
 *       src={scaleImageUrl('https://example.com/image-webp.webp', 200)}
 *       alt="优化后的图片"
 *     />
 *   );
 * };
 * ```
 */
const useScaleImageUrl = () => {
  const storeConfig = useAppSelector(storeConfigSelector)

  /**
   * 处理图片 URL 缩放和格式优化
   *
   * @param {string} [url=''] - 图片 URL，支持包含 '-webp.webp' 的 URL
   * @param {number} [width=375] - 图片宽度，用于计算缩放后的尺寸
   * @returns {string} 处理后的图片 URL，如果输入 URL 不符合处理条件则返回原 URL
   *
   * @description
   * 处理流程：
   * 1. 检查 URL 是否包含 WebP 标识符
   * 2. 获取设备像素比并计算实际宽度
   * 3. 使用正则表达式安全地解析 URL 参数
   * 4. 应用 WebP 格式转换和质量优化
   * 5. 保留原有 URL 参数并正确合并
   * 6. 返回优化后的 URL
   */
  const scaleImageUrl = useCallback(
    (url = '', width = 375) => {
      // 从 storeConfig 获取配置值，如果没有则使用默认值
      const webp = storeConfig?.webp || '-webp.webp'
      const webpformat = storeConfig?.webpformat || 'imageView2/2/w/{width}/format/webp/q/95'

      // 只处理包含 WebP 标识符的 URL
      if (typeof url === 'string' && url.includes(webp)) {
        // 获取设备像素比，向上取整以确保图片清晰度
        const dpr = Math.ceil(getDevicePixelRatio())

        // 创建正则表达式，安全地匹配 WebP 后缀和可能的查询参数
        // 使用 replace 方法转义特殊字符，避免正则表达式注入
        const regx = new RegExp(`${webp.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}(\\?.*)?$`)

        const match = url.match(regx)
        if (match) {
          // 提取原有的查询参数
          const params = match[1] || ''
          // 获取基础 URL（去除 WebP 后缀和参数）
          const base = url.replace(regx, '')

          // 计算实际需要的图片宽度（宽度 × 设备像素比）
          const actualWidth = Math.ceil(width * dpr)

          // 替换配置模板中的宽度占位符
          const processedConfig = webpformat.replace('{width}', actualWidth.toString())

          // 构建新的 URL
          let newUrl = `${base}?${processedConfig}`

          // 如果原 URL 有参数，需要正确合并
          if (params) {
            // params 格式为 '?a=1&b=2'，需要将 ? 替换为 & 来正确拼接
            newUrl += params.replace('?', '&')
          }

          return newUrl
        }
      }

      // 如果不符合处理条件，返回原 URL
      return url
    },
    [storeConfig],
  )

  return scaleImageUrl
}

export default useScaleImageUrl
