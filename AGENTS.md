# Repository Guidelines

## Rules

- Alaways respond in Chinese.

## Project Structure & Modules

- Monorepo managed by `pnpm` + Turborepo.
- Apps: `apps/web` (Next.js) and `apps/h5` (Next.js, mobile UI). Source lives under `apps/*/src`; static assets under `apps/*/public` (e.g., `apps/h5/public/fonts`).
- Shared packages: `packages/core` (business logic, hooks, services), `packages/eslint-config`, `packages/tailwind-config`.
- Workspace and pipelines: `pnpm-workspace.yaml`, `turbo.json`.

## Build, Test, and Development

- Install: `pnpm i` (Node ≥20, pnpm ≥9).
- Dev (all): `pnpm dev` to start apps via Turborepo.
- Dev (per app): `pnpm -F @ninebot/web dev` or `pnpm -F @ninebot/h5 dev`.
- Build: `pnpm build`; per app: `pnpm -F @ninebot/web build`.
- Lint/Types: `pnpm lint`, `pnpm type-check`.
- Format: `pnpm format`.
- GraphQL codegen: `pnpm -F @ninebot/web codegen:dev` or `codegen:prod` (uses `.env.*`).

## Coding Style & Naming

- TypeScript, React, Next.js. Prettier (with Tailwind plugin) formats; ESLint via `@ninebot/eslint-config`; Stylelint for CSS.
- 2-space indentation, semicolons off (Prettier defaults), single quotes where applicable.
- Components: `PascalCase.tsx`; hooks: `useX.ts`; re-exports via `index.ts`.
- CSS modules: `*.module.css`; utility files `camelCase.ts`.

## Testing Guidelines

- No unified test runner is defined yet. When adding tests, colocate as `*.test.ts` / `*.spec.tsx` near sources or under `__tests__`.
- Prefer React Testing Library for components and add Playwright for E2E if needed.
- Ensure `pnpm lint` and `pnpm type-check` pass before PRs.

## Commit & Pull Requests

- Conventional Commits are enforced (`feat:`, `fix:`, `docs:`…). Commitlint + Husky validate on push.
- PRs should include: clear description, linked issues, relevant screenshots for UI, and notes on env/config changes.
- Run `pnpm format && pnpm lint && pnpm type-check` before opening PRs.

## Security & Configuration

- Do not commit secrets. Use `.env.development` / `.env.production` per app; some scripts use `ENV_FILE` and PM2 configs under `apps/*/deploy`.
- Redis caching is used; confirm env credentials locally.

## Automations & Agents

- Keep changes minimal and package-scoped; avoid breaking shared `packages/core` APIs.
- Update docs when touching public interfaces or build/dev commands.
